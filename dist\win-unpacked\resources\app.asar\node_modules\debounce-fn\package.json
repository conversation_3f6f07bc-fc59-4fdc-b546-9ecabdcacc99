{"name": "debounce-fn", "version": "4.0.0", "description": "Debounce a function", "license": "MIT", "repository": "sindresorhus/debounce-fn", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "dependencies": {"mimic-fn": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "delay": "^4.2.0", "tsd": "^0.11.0", "xo": "^0.26.1"}}