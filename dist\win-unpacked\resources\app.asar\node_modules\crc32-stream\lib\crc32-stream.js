/**
 * node-crc32-stream
 *
 * Copyright (c) 2014 <PERSON>, contributors.
 * Licensed under the MIT license.
 * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT
 */

 'use strict';

const {Transform} = require('readable-stream');

const crc32 = require('crc-32');

class CRC32Stream extends Transform {
  constructor(options) {
    super(options);
    this.checksum = Buffer.allocUnsafe(4);
    this.checksum.writeInt32BE(0, 0);

    this.rawSize = 0;
  }

  _transform(chunk, encoding, callback) {
    if (chunk) {
      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;
      this.rawSize += chunk.length;
    }

    callback(null, chunk);
  }

  digest(encoding) {
    const checksum = Buffer.allocUnsafe(4);
    checksum.writeUInt32BE(this.checksum >>> 0, 0);
    return encoding ? checksum.toString(encoding) : checksum;
  }

  hex() {
    return this.digest('hex').toUpperCase();
  }

  size() {
    return this.rawSize;
  }
}

module.exports = CRC32Stream;
