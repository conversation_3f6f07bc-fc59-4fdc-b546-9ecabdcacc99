{"name": "archiver", "version": "7.0.1", "description": "a streaming interface for archive generation", "homepage": "https://github.com/archiverjs/node-archiver", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-archiver.git"}, "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 14"}, "dependencies": {"archiver-utils": "^5.0.2", "async": "^3.2.4", "buffer-crc32": "^1.0.0", "readable-stream": "^4.0.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^6.0.1"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "4.0.2", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5", "stream-bench": "0.1.2", "tar": "6.2.0", "yauzl": "3.1.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}}