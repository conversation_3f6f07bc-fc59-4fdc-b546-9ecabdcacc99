{"name": "archiver-utils", "version": "5.0.2", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/archiver-utils.git"}, "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 14"}, "dependencies": {"glob": "^10.0.0", "graceful-fs": "^4.2.0", "is-stream": "^2.0.1", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "devDependencies": {"chai": "4.4.1", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5"}}