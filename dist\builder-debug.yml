x64:
  firstOrDefaultFilePatterns:
    - '**/*'
    - '!**/node_modules/**'
    - '!build{,/**/*}'
    - '!dist{,/**/*}'
    - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
    - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
    - '!**/node_modules/*.d.ts'
    - '!**/node_modules/.bin'
    - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
    - '!.editorconfig'
    - '!**/._*'
    - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
    - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
    - '!**/{appveyor.yml,.travis.yml,circle.yml}'
    - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
    - '!**/dist/*'
    - package.json
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,obj,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,pnpm-lock.yaml,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - '**/*'
    - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
    - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
    - '!**/node_modules/*.d.ts'
    - '!**/node_modules/.bin'
    - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
    - '!.editorconfig'
    - '!**/._*'
    - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
    - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
    - '!**/{appveyor.yml,.travis.yml,circle.yml}'
    - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
    - '!**/dist/*'
