{"name": "locate-path", "version": "3.0.0", "description": "Get the first path that exists on disk of multiple paths", "license": "MIT", "repository": "sindresorhus/locate-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "files": ["index.js"], "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}