{"name": "json-schema-traverse", "version": "1.0.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"eslint": "^7.3.1", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.2.2"}}