{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["\"use strict\";\n\nexport let JSONSchemaFormat;\n\n(function (JSONSchemaFormat) {\n  JSONSchemaFormat[\"Date\"] = \"date\";\n  JSONSchemaFormat[\"DateTime\"] = \"date-time\";\n  JSONSchemaFormat[\"Email\"] = \"email\";\n  JSONSchemaFormat[\"Hostname\"] = \"hostname\";\n  JSONSchemaFormat[\"IDNEmail\"] = \"idn-email\";\n  JSONSchemaFormat[\"IDNHostname\"] = \"idn-hostname\";\n  JSONSchemaFormat[\"IPv4\"] = \"ipv4\";\n  JSONSchemaFormat[\"IPv6\"] = \"ipv6\";\n  JSONSchemaFormat[\"IRI\"] = \"iri\";\n  JSONSchemaFormat[\"IRIReference\"] = \"iri-reference\";\n  JSONSchemaFormat[\"JSONPointer\"] = \"json-pointer\";\n  JSONSchemaFormat[\"JSONPointerURIFragment\"] = \"json-pointer-uri-fragment\";\n  JSONSchemaFormat[\"RegEx\"] = \"regex\";\n  JSONSchemaFormat[\"RelativeJSONPointer\"] = \"relative-json-pointer\";\n  JSONSchemaFormat[\"Time\"] = \"time\";\n  JSONSchemaFormat[\"URI\"] = \"uri\";\n  JSONSchemaFormat[\"URIReference\"] = \"uri-reference\";\n  JSONSchemaFormat[\"URITemplate\"] = \"uri-template\";\n  JSONSchemaFormat[\"UUID\"] = \"uuid\";\n})(JSONSchemaFormat || (JSONSchemaFormat = {}));\n\nexport let JSONSchemaType;\n\n(function (JSONSchemaType) {\n  JSONSchemaType[\"Array\"] = \"array\";\n  JSONSchemaType[\"Boolean\"] = \"boolean\";\n  JSONSchemaType[\"Integer\"] = \"integer\";\n  JSONSchemaType[\"Null\"] = \"null\";\n  JSONSchemaType[\"Number\"] = \"number\";\n  JSONSchemaType[\"Object\"] = \"object\";\n  JSONSchemaType[\"String\"] = \"string\";\n})(JSONSchemaType || (JSONSchemaType = {}));\n\nexport let JSONSchemaContentEncoding;\n\n(function (JSONSchemaContentEncoding) {\n  JSONSchemaContentEncoding[\"7bit\"] = \"7bit\";\n  JSONSchemaContentEncoding[\"8bit\"] = \"8bit\";\n  JSONSchemaContentEncoding[\"Binary\"] = \"binary\";\n  JSONSchemaContentEncoding[\"QuotedPrintable\"] = \"quoted-printable\";\n  JSONSchemaContentEncoding[\"Base64\"] = \"base64\";\n  JSONSchemaContentEncoding[\"IETFToken\"] = \"ietf-token\";\n  JSONSchemaContentEncoding[\"XToken\"] = \"x-token\";\n})(JSONSchemaContentEncoding || (JSONSchemaContentEncoding = {}));\n\nexport const JSONSchemaKeys = ['$comment', '$id', '$ref', '$schema', 'additionalItems', 'additionalProperties', 'allOf', 'anyOf', 'const', 'contains', 'contentEncoding', 'contentMediaType', 'default', 'definitions', 'dependencies', 'description', 'else', 'enum', 'examples', 'exclusiveMaximum', 'exclusiveMinimum', 'format', 'if', 'items', 'maximum', 'maxItems', 'maxLength', 'maxProperties', 'minimum', 'minItems', 'minLength', 'minProperties', 'multipleOf', 'not', 'oneOf', 'pattern', 'patternProperties', 'properties', 'propertyNames', 'readOnly', 'required', 'then', 'title', 'type', 'uniqueItems', 'writeOnly'];"], "names": ["JSONSchemaFormat", "JSONSchemaType", "JSONSchemaContentEncoding", "JSONSchemaKeys"], "mappings": ";;;;AAIA,CAAC,UAAUA,gBAAV,EAA4B;EAC3BA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,MAA3B;EACAA,gBAAgB,CAAC,UAAD,CAAhB,GAA+B,WAA/B;EACAA,gBAAgB,CAAC,OAAD,CAAhB,GAA4B,OAA5B;EACAA,gBAAgB,CAAC,UAAD,CAAhB,GAA+B,UAA/B;EACAA,gBAAgB,CAAC,UAAD,CAAhB,GAA+B,WAA/B;EACAA,gBAAgB,CAAC,aAAD,CAAhB,GAAkC,cAAlC;EACAA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,MAA3B;EACAA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,MAA3B;EACAA,gBAAgB,CAAC,KAAD,CAAhB,GAA0B,KAA1B;EACAA,gBAAgB,CAAC,cAAD,CAAhB,GAAmC,eAAnC;EACAA,gBAAgB,CAAC,aAAD,CAAhB,GAAkC,cAAlC;EACAA,gBAAgB,CAAC,wBAAD,CAAhB,GAA6C,2BAA7C;EACAA,gBAAgB,CAAC,OAAD,CAAhB,GAA4B,OAA5B;EACAA,gBAAgB,CAAC,qBAAD,CAAhB,GAA0C,uBAA1C;EACAA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,MAA3B;EACAA,gBAAgB,CAAC,KAAD,CAAhB,GAA0B,KAA1B;EACAA,gBAAgB,CAAC,cAAD,CAAhB,GAAmC,eAAnC;EACAA,gBAAgB,CAAC,aAAD,CAAhB,GAAkC,cAAlC;EACAA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,MAA3B;CAnBF,EAoBGA,wBAAgB,KAAKA,wBAAgB,GAAG,EAAxB,CApBnB;;AAwBA,CAAC,UAAUC,cAAV,EAA0B;EACzBA,cAAc,CAAC,OAAD,CAAd,GAA0B,OAA1B;EACAA,cAAc,CAAC,SAAD,CAAd,GAA4B,SAA5B;EACAA,cAAc,CAAC,SAAD,CAAd,GAA4B,SAA5B;EACAA,cAAc,CAAC,MAAD,CAAd,GAAyB,MAAzB;EACAA,cAAc,CAAC,QAAD,CAAd,GAA2B,QAA3B;EACAA,cAAc,CAAC,QAAD,CAAd,GAA2B,QAA3B;EACAA,cAAc,CAAC,QAAD,CAAd,GAA2B,QAA3B;CAPF,EAQGA,sBAAc,KAAKA,sBAAc,GAAG,EAAtB,CARjB;;AAYA,CAAC,UAAUC,yBAAV,EAAqC;EACpCA,yBAAyB,CAAC,MAAD,CAAzB,GAAoC,MAApC;EACAA,yBAAyB,CAAC,MAAD,CAAzB,GAAoC,MAApC;EACAA,yBAAyB,CAAC,QAAD,CAAzB,GAAsC,QAAtC;EACAA,yBAAyB,CAAC,iBAAD,CAAzB,GAA+C,kBAA/C;EACAA,yBAAyB,CAAC,QAAD,CAAzB,GAAsC,QAAtC;EACAA,yBAAyB,CAAC,WAAD,CAAzB,GAAyC,YAAzC;EACAA,yBAAyB,CAAC,QAAD,CAAzB,GAAsC,SAAtC;CAPF,EAQGA,iCAAyB,KAAKA,iCAAyB,GAAG,EAAjC,CAR5B;;AAUA,MAAaC,cAAc,GAAG,CAAC,UAAD,EAAa,KAAb,EAAoB,MAApB,EAA4B,SAA5B,EAAuC,iBAAvC,EAA0D,sBAA1D,EAAkF,OAAlF,EAA2F,OAA3F,EAAoG,OAApG,EAA6G,UAA7G,EAAyH,iBAAzH,EAA4I,kBAA5I,EAAgK,SAAhK,EAA2K,aAA3K,EAA0L,cAA1L,EAA0M,aAA1M,EAAyN,MAAzN,EAAiO,MAAjO,EAAyO,UAAzO,EAAqP,kBAArP,EAAyQ,kBAAzQ,EAA6R,QAA7R,EAAuS,IAAvS,EAA6S,OAA7S,EAAsT,SAAtT,EAAiU,UAAjU,EAA6U,WAA7U,EAA0V,eAA1V,EAA2W,SAA3W,EAAsX,UAAtX,EAAkY,WAAlY,EAA+Y,eAA/Y,EAAga,YAAha,EAA8a,KAA9a,EAAqb,OAArb,EAA8b,SAA9b,EAAyc,mBAAzc,EAA8d,YAA9d,EAA4e,eAA5e,EAA6f,UAA7f,EAAygB,UAAzgB,EAAqhB,MAArhB,EAA6hB,OAA7hB,EAAsiB,MAAtiB,EAA8iB,aAA9iB,EAA6jB,WAA7jB,CAAvB;;;;"}