{"name": "landing", "version": "1.0.0", "description": "Landing Page Creator", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --win --dir", "dist": "electron-builder --win --dir"}, "build": {"appId": "com.landing.app", "productName": "Landing Page Creator", "directories": {"output": "dist"}, "win": {"target": ["portable"], "icon": "build/icon.ico"}, "portable": {"artifactName": "LandingPageCreator.exe"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!**/dist/*"]}, "author": "", "license": "ISC", "devDependencies": {"electron": "^29.0.0", "electron-builder": "^26.0.12", "png-to-ico": "^2.1.8"}, "dependencies": {"archiver": "^7.0.1", "electron-store": "^8.1.0", "rimraf": "^6.0.1"}}