if('serviceWorker'in navigator){window.addEventListener('load',function(){navigator.serviceWorker.register('/service-worker.js').then(function(registration){console.log('ServiceWorker registration successful with scope: ',registration.scope);},function(err){console.log('ServiceWorker registration failed: ',err);});});}
function preloadImages(){const images=['images/bg.jpg','images/logo.jpg','images/1.png'];images.forEach(image=>{const img=new Image();img.src=image;});}
document.addEventListener('DOMContentLoaded',function(){preloadImages();updateCurrentDate();updateRandomStats();loadTestimonials();setupStepButtons();setupDeviceSelection();setupProxySelection();if(window.performance){const perfData=window.performance.timing;const pageLoadTime=perfData.loadEventEnd-perfData.navigationStart;console.log('Sayfa yükleme süresi: '+pageLoadTime+'ms');}});function updateCurrentDate(){const currentDateElement=document.getElementById('current-date');const postDateElement=document.getElementById('post-date');const now=new Date();const options={year:'numeric',month:'long',day:'numeric'};currentDateElement.textContent=now.toLocaleDateString('tr-TR',options);postDateElement.textContent=now.toLocaleDateString('tr-TR',options);}
function updateRandomStats(){const onlineCount=document.getElementById('online-count');const successCount=document.getElementById('success-count');setInterval(()=>{const randomChange=Math.floor(Math.random()*21)-10;let currentCount=parseInt(onlineCount.textContent);currentCount+=randomChange;if(currentCount<450)currentCount=450;if(currentCount>550)currentCount=550;onlineCount.textContent=currentCount;},5000);function updateSuccessCount(){const now=new Date();const hour=now.getHours();const minute=now.getMinutes();let minutesSince7AM=0;if(hour>=7){minutesSince7AM=(hour-7)*60+minute;}else{minutesSince7AM=(hour+17)*60+minute;}
const increment=719/1440;const currentSuccessCount=Math.floor(25+(minutesSince7AM*increment));const finalCount=Math.min(currentSuccessCount,744);successCount.textContent=finalCount;}
updateSuccessCount();setInterval(updateSuccessCount,60000);}
function loadTestimonials(){const testimonials=[{name:"Ahmet",message:"Teşekkürler! 25000 Para hesabıma yüklendi!"},{name:"Mehmet",message:"Harika çalışıyor, arkadaşlarıma da önereceğim!"},{name:"Ayşe",message:"İnanamıyorum, gerçekten ücretsiz Para kazandım!"},{name:"Fatma",message:"Bu kadar kolay olacağını düşünmemiştim, teşekkürler!"},{name:"Ali",message:"Sonunda çalışan bir Para hilesi buldum!"},{name:"Zeynep",message:"SMS doğrulaması sonrası Paralar anında geldi!"},{name:"Mustafa",message:"Artık Ali'ta rakiplerime fark atıyorum!"},{name:"Emine",message:"Bu hileyi keşfettiğim için çok mutluyum!"},{name:"Hüseyin",message:"Arkadaşlarım kıskançlıktan çatlıyor!"},{name:"Hatice",message:"Paralar sayesinde tüm karakterleri açtım!"},{name:"Osman",message:"Bu site sayesinde oyunda çok ilerledim!"},{name:"Gül",message:"Paralar gerçekten hesabıma yüklendi, teşekkürler!"},{name:"İbrahim",message:"Artık premium içeriklere erişebiliyorum!"},{name:"Sevgi",message:"Ali'ta artık daha güçlüyüm!"},{name:"Kemal",message:"Arkadaşlarım nasıl bu kadar Para kazandığımı soruyor!"},{name:"Seda",message:"SMS doğrulaması çok kolaydı, Paralar anında geldi!"},{name:"Murat",message:"Bu hile sayesinde oyunda lider oldum!"},{name:"Deniz",message:"Gerçekten çalışıyor, herkese tavsiye ederim!"},{name:"Serkan",message:"Paralar sayesinde tüm karakterleri geliştirdim!"},{name:"Ece",message:"Bu kadar kolay olacağını düşünmemiştim, harika!"},{name:"Burak",message:"50000 Para kazandım, oyunda artık yenilmez oldum!"},{name:"Canan",message:"Arkadaşlarımın hepsine bu siteyi önerdim!"},{name:"Emre",message:"Ali'ta artık en güçlü karakterlere sahibim!"},{name:"Gamze",message:"Paraları kullanarak tüm skin'leri aldım!"},{name:"Hakan",message:"Bu kadar hızlı olacağını tahmin etmemiştim!"},{name:"Jale",message:"Oyunda artık istediğim her şeyi alabiliyorum!"},{name:"Kadir",message:"Paraları kullanarak oyunda çok hızlı ilerledim!"},{name:"Leyla",message:"Ali'ta artık en yüksek seviyedeyim!"},{name:"Metin",message:"Bu site sayesinde oyun deneyimim tamamen değişti!"},{name:"Nalan",message:"Paraları kullanarak tüm bölümleri geçtim!"},{name:"Orhan",message:"Arkadaşlarımın arasında en güçlü oyuncu benim!"}];const testimonialsContainer=document.getElementById('testimonials');testimonials.forEach(item=>{const testimonialElement=document.createElement('div');testimonialElement.className='testimonial';testimonialElement.innerHTML=`<strong>${item.name}:</strong> ${item.message}`;testimonialsContainer.appendChild(testimonialElement);});testimonials.forEach(item=>{const testimonialElement=document.createElement('div');testimonialElement.className='testimonial';testimonialElement.innerHTML=`<strong>${item.name}:</strong> ${item.message}`;testimonialsContainer.appendChild(testimonialElement);});}
function setupStepButtons(){document.getElementById('step1-next').addEventListener('click',function(){const username=document.getElementById('username').value.trim();if(username===''){alert('Lütfen kullanıcı adınızı girin!');return;}
document.getElementById('step1').style.display='none';document.getElementById('step2').style.display='block';});document.getElementById('step2-next').addEventListener('click',function(){const gemsInput=document.getElementById('gems-input');const gemsValue=parseInt(gemsInput.value);if(isNaN(gemsValue)||gemsValue<=0){alert('Lütfen geçerli bir Para miktarı girin!');return;}
document.getElementById('step2').style.display='none';document.getElementById('step3').style.display='block';});document.getElementById('step3-next').addEventListener('click',function(){document.getElementById('step3').style.display='none';document.getElementById('step4').style.display='block';startGenerating();});}
function setupDeviceSelection(){const deviceButtons=document.querySelectorAll('.device-btn');deviceButtons.forEach(button=>{button.addEventListener('click',function(){deviceButtons.forEach(btn=>btn.classList.remove('active'));this.classList.add('active');});});}
function setupProxySelection(){const proxyButtons=document.querySelectorAll('.proxy-btn');const nextButton=document.getElementById('step3-next');proxyButtons.forEach(button=>{button.addEventListener('click',function(){proxyButtons.forEach(btn=>btn.classList.remove('active'));this.classList.add('active');nextButton.disabled=false;});});}
function startGenerating(){const consoleElement=document.getElementById('console');const username=document.getElementById('username').value;const gemsAmount=document.getElementById('gems-input').value;const device=document.querySelector('.device-btn.active').dataset.device;const proxy=document.querySelector('.proxy-btn.active').dataset.proxy;const messages=[`${username} isimli kullanıcı Ali oyun datasında aranıyor...`,`${username} bulundu.`,`${gemsAmount} Para seçilen ${proxy} proxy üzerinden ${getDeviceName(device)} cihazındaki uygulamaya yükleniyor...`,`Bağlantı kuruluyor...`,`Sunucu yanıt veriyor...`,`Paralar hazırlanıyor...`,`Ücretsiz kullanıcı telefon SMS doğrulaması gerekiyor...`];const totalDuration=10000;const messageInterval=totalDuration/messages.length;let index=0;const interval=setInterval(()=>{if(index<messages.length){const messageElement=document.createElement('p');let messageText=messages[index];if(index===0||index===1){messageText=messageText.replace(username,`<span class="highlight-info">${username}</span>`);}else if(index===2){messageText=messageText.replace(username,`<span class="highlight-info">${username}</span>`);messageText=messageText.replace(gemsAmount,`<span class="highlight-info">${gemsAmount}</span>`);messageText=messageText.replace(getProxyName(proxy),`<span class="highlight-info">${getProxyName(proxy)}</span>`);messageText=messageText.replace(getDeviceName(device),`<span class="highlight-info">${getDeviceName(device)}</span>`);}
messageElement.innerHTML=messageText;consoleElement.appendChild(messageElement);consoleElement.scrollTop=consoleElement.scrollHeight;index++;}else{clearInterval(interval);document.getElementById('step4').style.display='none';document.getElementById('step5').style.display='block';setupVerification(username,gemsAmount,device,proxy);startCountdown();}},messageInterval);}
function setupVerification(username,gemsAmount,device,proxy){document.getElementById('verify-username').textContent=username;document.getElementById('verify-gems').textContent=gemsAmount;document.getElementById('verify-device').textContent=getDeviceName(device);document.getElementById('verify-proxy').textContent=getProxyName(proxy);document.getElementById('verify-button').href="https://verifyuser.org/cl/i/kldqr9";}
function startCountdown(){const countdownElement=document.getElementById('countdown-timer');let minutes=10;let seconds=0;const interval=setInterval(()=>{if(seconds===0){if(minutes===0){clearInterval(interval);countdownElement.textContent="Süre doldu!";return;}
minutes--;seconds=59;}else{seconds--;}
const formattedMinutes=minutes.toString().padStart(2,'0');const formattedSeconds=seconds.toString().padStart(2,'0');countdownElement.textContent=`${formattedMinutes}:${formattedSeconds}`;},1000);}
function getDeviceName(device){switch(device){case 'android':return 'Android';case 'ios':return 'iOS';case 'pc':return 'PC';case 'mac':return 'MAC';default:return device;}}
function getProxyName(proxy){switch(proxy){case 'avrupa':return 'Avrupa';case 'amerika':return 'Amerika';case 'afrika':return 'Afrika';case 'rusya':return 'Rusya';default:return proxy;}}




