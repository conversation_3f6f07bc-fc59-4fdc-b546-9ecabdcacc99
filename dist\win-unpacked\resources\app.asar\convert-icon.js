const fs = require('fs');
const path = require('path');

// JPG dosyasını PNG'ye çevirmek için sharp kullanmak yerine
// Basit bir çözüm: JPG dosyasını doğrudan kopyalayıp ICO olarak kullanmaya çalışalım
// Veya manuel olarak bir PNG/ICO dosyası oluşturalım

console.log('Icon conversion script');
console.log('landıng.jpg dosyası bulundu, manuel olarak ICO formatına çevrilmesi gerekiyor.');
console.log('Alternatif: Online bir converter kullanarak landıng.jpg -> icon.ico çevirimi yapabilirsiniz.');
console.log('Veya build klasöründeki mevcut icon.ico dosyasını landıng.jpg ile değiştirebiliriz.');

// Mevcut icon.ico dosyasını kontrol et
const currentIconPath = path.join(__dirname, 'build', 'icon.ico');
const newImagePath = path.join(__dirname, 'landıng.jpg');

if (fs.existsSync(currentIconPath)) {
    console.log('Mevcut icon.ico dosyası bulundu:', currentIconPath);
}

if (fs.existsSync(newImagePath)) {
    console.log('Yeni resim dosyası bulundu:', newImagePath);
    console.log('Bu dosyayı ICO formatına çevirmek için online converter kullanın:');
    console.log('- https://convertio.co/jpg-ico/');
    console.log('- https://www.icoconverter.com/');
    console.log('Sonra build/icon.ico dosyasını değiştirin.');
}
