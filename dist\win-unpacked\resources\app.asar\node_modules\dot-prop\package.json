{"name": "dot-prop", "version": "6.0.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.13.1", "xo": "^0.33.1"}}