{"name": "electron-store", "version": "8.2.0", "description": "Simple data persistence for your Electron app or module - Save and load user settings, app state, cache, etc", "license": "MIT", "repository": "sindresorhus/electron-store", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "sideEffects": false, "files": ["index.js", "index.d.ts"], "dependencies": {"conf": "^10.2.0", "type-fest": "^2.17.0"}, "devDependencies": {"ava": "^2.4.0", "electron": "^12.0.4", "execa": "^5.0.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}