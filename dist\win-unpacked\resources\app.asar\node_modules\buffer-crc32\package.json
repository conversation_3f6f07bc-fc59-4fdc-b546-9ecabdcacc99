{"author": "<PERSON> <<EMAIL>>", "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "files": ["dist", "index.d.ts", "LICENSE", "README.md"]}