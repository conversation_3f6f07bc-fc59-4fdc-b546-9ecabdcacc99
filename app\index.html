<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; script-src 'self' 'unsafe-inline';">
    <title>Landing - CPA Landing Page Creator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        /* Kompakt form alanları için stil */
        .compact-form {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 700px;
            margin-bottom: 20px;
        }

        .compact-form .form-group {
            width: 32%;
            margin-right: 0;
        }

        /* Resim seçme alanları için stil */
        .image-inputs {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .image-inputs .form-group {
            width: 32%;
            margin-right: 0;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input[type="text"],
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border 0.3s;
        }

        input[type="text"]:focus,
        textarea:focus {
            border-color: #3498db;
            outline: none;
        }

        .file-input-group {
            display: flex;
            align-items: center;
        }

        .file-input-group input[type="text"] {
            flex-grow: 1;
            margin-right: 10px;
        }

        .file-input-group button {
            padding: 12px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .file-input-group button:hover {
            background-color: #2980b9;
        }

        /* Sürükle bırak için stiller */
        .file-drop-area {
            position: relative;
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .file-drop-area.drag-over {
            border-color: #3498db;
            background-color: rgba(52, 152, 219, 0.1);
        }

        .file-drop-text {
            text-align: center;
            color: #777;
            font-size: 14px;
            margin-bottom: 5px;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn-create {
            display: inline-block;
            width: 48%;
            padding: 15px;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 30px;
        }

        .btn-create:hover {
            background-color: #27ae60;
        }

        .btn-zip {
            display: inline-block;
            width: 48%;
            padding: 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 30px;
            margin-left: 4%;
        }

        .btn-zip:hover {
            background-color: #c0392b;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: block;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: block;
        }

        .language-selector {
            text-align: right;
            margin-bottom: 20px;
        }

        .language-btn {
            padding: 5px 10px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 5px;
        }

        .language-btn.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="language-selector">
            <button class="language-btn active" data-lang="tr">TR</button>
            <button class="language-btn" data-lang="en">ENG</button>
        </div>

        <h1 id="title">Landing Page Oluşturucu</h1>

        <!-- Kompakt form alanı - Oyun adı, para birimi ve CPA linki yan yana -->
        <div class="compact-form">
            <div class="form-group">
                <label id="label-game-name">Oyun Adı:</label>
                <input type="text" id="game-name" placeholder="Örn: Brawl Stars">
            </div>

            <div class="form-group">
                <label id="label-currency">Para Birimi:</label>
                <input type="text" id="currency" placeholder="Örn: elmas">
            </div>

            <div class="form-group">
                <label id="label-cpa-link">CPA Linki:</label>
                <input type="text" id="cpa-link" placeholder="Örn: https://example.com/cpa-link">
            </div>
			
			<div class="form-group">
                <label id="label-twitter-url">Twitter URL:</label>
                <input type="text" id="twitter-url" placeholder="Örn: https://x.com/intent/post?text=..." value="https://twitter.com/intent/tweet?url=https://www.brawlstarselmas.com&text=Brawl%20Stars%20Elmas%20Hilesi%20ile%20ücretsiz%20elmas%20kazanın!">
            </div>

            <div class="form-group">
                <label id="label-youtube-url">Youtube URL:</label>
                <input type="text" id="youtube-url" placeholder="Örn: https://www.youtube.com/@senmakiraz4690/channels" value="https://www.youtube.com/@senmakiraz4690/channels">
            </div>
        </div>

        <!-- Resim seçme alanları yan yana -->
        <div class="image-inputs">
            <div class="form-group">
                <label id="label-logo-image">Logo Resmi:</label>
                <div class="file-drop-area" id="logo-drop-area">
                    <div class="file-drop-text" id="logo-drop-text">Resmi buraya sürükleyip bırakın</div>
                    <div class="file-input-group">
                        <input type="text" id="logo-image-path" readonly>
                        <button id="select-logo-btn" class="select-file-btn">Seç</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label id="label-bg-image">Arkaplan Resmi:</label>
                <div class="file-drop-area" id="bg-drop-area">
                    <div class="file-drop-text" id="bg-drop-text">Resmi buraya sürükleyip bırakın</div>
                    <div class="file-input-group">
                        <input type="text" id="bg-image-path" readonly>
                        <button id="select-bg-btn" class="select-file-btn">Seç</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label id="label-currency-image">Para Birimi Resmi:</label>
                <div class="file-drop-area" id="currency-drop-area">
                    <div class="file-drop-text" id="currency-drop-text">Resmi buraya sürükleyip bırakın</div>
                    <div class="file-input-group">
                        <input type="text" id="currency-image-path" readonly>
                        <button id="select-currency-btn" class="select-file-btn">Seç</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <label id="label-tracking-code">Tracking Kodu (Opsiyonel):</label>
                <div>
                    <button id="save-tracking-btn" style="padding: 5px 10px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 5px;">
                        <i class="fas fa-save"></i> Kaydet
                    </button>
                    <button id="favorites-btn" style="padding: 5px 10px; background-color: #f1f1f1; border: 1px solid #ddd; border-radius: 3px; cursor: pointer;">
                        <i class="fas fa-star"></i> Sık Kullanılanlar
                    </button>
                </div>
            </div>
            <textarea id="tracking-code" placeholder="Örn: Google Analytics veya diğer tracking kodları"></textarea>
            <div id="favorites-dropdown" style="display: none; position: absolute; background-color: white; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 100; width: 300px; max-height: 300px; overflow-y: auto;">
                <div style="padding: 10px; border-bottom: 1px solid #ddd;">
                    <p style="margin: 0 0 10px 0; font-weight: bold;">Sık Kullanılan Tracking Kodları</p>
                    <div id="favorites-list" style="max-height: 200px; overflow-y: auto;">
                        <!-- Sık kullanılanlar buraya eklenecek -->
                    </div>
                </div>
            </div>
        </div>

        <div style="display: flex; justify-content: space-between;">
            <button id="create-btn" class="btn-create">Oluştur</button>
            <button id="zip-btn" class="btn-zip">Ziple</button>
        </div>

        <!-- Oluşturulan Landing Page'ler Listesi -->
        <div id="landing-pages-container" style="margin-top: 30px;">
            <h3 style="margin-bottom: 15px;">Oluşturulan Landing Page'ler</h3>
            <div id="landing-pages-list" style="border: 1px solid #ddd; border-radius: 5px; max-height: 300px; overflow-y: auto;">
                <p style="text-align: center; padding: 20px; color: #666;">Henüz oluşturulmuş landing page yok</p>
            </div>
        </div>

        <div id="status" class="status"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        // Dil değiştirme
        const translations = {
            tr: {
                title: "Landing Page Oluşturucu",
                labelGameName: "Oyun Adı:",
                labelCurrency: "Para Birimi:",
                labelCpaLink: "CPA Linki:",
                labelTwitterUrl: "Twitter URL'si:",
                labelYoutubeUrl: "Youtube URL'si:",
                labelLogoImage: "Logo Resmi:",
                labelBgImage: "Arkaplan Resmi:",
                labelCurrencyImage: "Para Birimi Resmi:",
                labelTrackingCode: "Tracking Kodu (Opsiyonel):",
                selectBtn: "Seç",
                createBtn: "Oluştur",
                zipBtn: "Ziple",
                saveBtn: "Kaydet",
                deleteBtn: "Sil",
                openFolderBtn: "Klasörü Aç",
                successMsg: "Landing page başarıyla oluşturuldu!",
                zipSuccessMsg: "Landing page başarıyla oluşturuldu ve ziplendi!",
                errorMsg: "Hata oluştu: ",
                placeholderGameName: "Örn: Brawl Stars",
                placeholderCurrency: "Örn: elmas",
                placeholderCpaLink: "Örn: https://example.com/cpa-link",
                placeholderTwitterUrl: "Örn: https://x.com/intent/post?text=...",
                placeholderYoutubeUrl: "Örn: https://www.youtube.com/@senmakiraz4690/channels",
                placeholderTrackingCode: "Örn: Google Analytics veya diğer tracking kodları",
                validationError: "Lütfen tüm zorunlu alanları doldurun!",
                favoritesBtn: "Sık Kullanılanlar",
                noFavorites: "Henüz kayıtlı kod yok",
                noLandingPages: "Henüz oluşturulmuş landing page yok",
                landingPagesTitle: "Oluşturulan Landing Page'ler",
                saveFavoriteConfirm: "Bu tracking kodunu sık kullanılanlara kaydetmek ister misiniz?",
                favoriteTitle: "Bu kod için bir başlık girin:",
                favoritesTitle: "Sık Kullanılan Tracking Kodları",
                enterTrackingCode: "Lütfen bir tracking kodu girin!",
                trackingSaved: "Tracking kodu başarıyla kaydedildi!",
                trackingSaveError: "Tracking kodu kaydedilirken bir hata oluştu!",
                deleteLandingConfirm: "Bu landing page'i silmek istediğinize emin misiniz?",
                landingPageDeleted: "Landing page başarıyla silindi!"
            },
            en: {
                title: "Landing Page Creator",
                labelGameName: "Game Name:",
                labelCurrency: "Currency:",
                labelCpaLink: "CPA Link:",
                labelTwitterUrl: "Twitter URL:",
                labelYoutubeUrl: "Youtube URL:",
                labelLogoImage: "Logo Image:",
                labelBgImage: "Background Image:",
                labelCurrencyImage: "Currency Image:",
                labelTrackingCode: "Tracking Code (Optional):",
                selectBtn: "Select",
                createBtn: "Create",
                zipBtn: "Zip",
                saveBtn: "Save",
                deleteBtn: "Delete",
                openFolderBtn: "Open Folder",
                successMsg: "Landing page created successfully!",
                zipSuccessMsg: "Landing page created and zipped successfully!",
                errorMsg: "Error occurred: ",
                placeholderGameName: "Ex: Brawl Stars",
                placeholderCurrency: "Ex: gems",
                placeholderCpaLink: "Ex: https://example.com/cpa-link",
                labelTwitterUrl: "Twitter URL:",
                labelYoutubeUrl: "Youtube URL:",
                placeholderTrackingCode: "Ex: Google Analytics or other tracking codes",
                validationError: "Please fill in all required fields!",
                favoritesBtn: "Favorites",
                noFavorites: "No saved codes yet",
                noLandingPages: "No landing pages created yet",
                landingPagesTitle: "Created Landing Pages",
                saveFavoriteConfirm: "Do you want to save this tracking code to favorites?",
                favoriteTitle: "Enter a title for this code:",
                favoritesTitle: "Favorite Tracking Codes",
                enterTrackingCode: "Please enter a tracking code!",
                trackingSaved: "Tracking code saved successfully!",
                trackingSaveError: "An error occurred while saving the tracking code!",
                deleteLandingConfirm: "Are you sure you want to delete this landing page?",
                landingPageDeleted: "Landing page deleted successfully!"
            }
        };

        let currentLang = 'tr';

        // Dil değiştirme butonları
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const lang = btn.dataset.lang;
                if (lang !== currentLang) {
                    currentLang = lang;
                    updateLanguage();

                    // Aktif buton sınıfını güncelle
                    document.querySelectorAll('.language-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                }
            });
        });

        // Dil güncellemesi
        function updateLanguage() {
            const t = translations[currentLang];

            document.getElementById('title').textContent = t.title;
            document.getElementById('label-game-name').textContent = t.labelGameName;
            document.getElementById('label-currency').textContent = t.labelCurrency;
            document.getElementById('label-cpa-link').textContent = t.labelCpaLink;
            document.getElementById('label-twitter-url').textContent = t.labelTwitterUrl;
            document.getElementById('label-youtube-url').textContent = t.labelYoutubeUrl;
            document.getElementById('label-logo-image').textContent = t.labelLogoImage;
            document.getElementById('label-bg-image').textContent = t.labelBgImage;
            document.getElementById('label-currency-image').textContent = t.labelCurrencyImage;
            document.getElementById('label-tracking-code').textContent = t.labelTrackingCode;

            document.getElementById('game-name').placeholder = t.placeholderGameName;
            document.getElementById('currency').placeholder = t.placeholderCurrency;
            document.getElementById('cpa-link').placeholder = t.placeholderCpaLink;
            document.getElementById('twitter-url').placeholder = t.placeholderTwitterUrl;
            document.getElementById('youtube-url').placeholder = t.placeholderYoutubeUrl;
            document.getElementById('tracking-code').placeholder = t.placeholderTrackingCode;

            document.getElementById('create-btn').textContent = t.createBtn;
            document.getElementById('zip-btn').textContent = t.zipBtn;

            // Landing page listesi başlığını güncelle
            document.querySelector('#landing-pages-container h3').textContent = t.landingPagesTitle;

            // Boş landing page mesajını güncelle
            const emptyLandingPageMsg = document.querySelector('#landing-pages-list p');
            if (emptyLandingPageMsg && emptyLandingPageMsg.parentElement.children.length === 1) {
                emptyLandingPageMsg.textContent = t.noLandingPages;
            }
            document.querySelectorAll('.select-file-btn').forEach(btn => {
                btn.textContent = t.selectBtn;
            });

            // Sık kullanılanlar butonunu güncelle
            document.getElementById('favorites-btn').innerHTML = `<i class="fas fa-star"></i> ${t.favoritesBtn}`;

            // Kaydet butonunu güncelle
            document.getElementById('save-tracking-btn').innerHTML = `<i class="fas fa-save"></i> ${t.saveBtn}`;

            // Sık kullanılanlar başlığını güncelle
            const favoritesTitle = document.querySelector('#favorites-dropdown p');
            if (favoritesTitle) {
                favoritesTitle.textContent = t.favoritesTitle;
            }

            // Sürükle bırak metinlerini güncelle
            document.getElementById('logo-drop-text').textContent = currentLang === 'tr' ? 'Resmi buraya sürükleyip bırakın' : 'Drag and drop image here';
            document.getElementById('bg-drop-text').textContent = currentLang === 'tr' ? 'Resmi buraya sürükleyip bırakın' : 'Drag and drop image here';
            document.getElementById('currency-drop-text').textContent = currentLang === 'tr' ? 'Resmi buraya sürükleyip bırakın' : 'Drag and drop image here';
        }

        // Dosya seçme işlemleri
        document.getElementById('select-logo-btn').addEventListener('click', async () => {
            const filePaths = await ipcRenderer.invoke('select-file', {
                properties: ['openFile'],
                filters: [
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
                ]
            });

            if (filePaths && filePaths.length > 0) {
                document.getElementById('logo-image-path').value = filePaths[0];
            }
        });

        document.getElementById('select-bg-btn').addEventListener('click', async () => {
            const filePaths = await ipcRenderer.invoke('select-file', {
                properties: ['openFile'],
                filters: [
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
                ]
            });

            if (filePaths && filePaths.length > 0) {
                document.getElementById('bg-image-path').value = filePaths[0];
            }
        });

        document.getElementById('select-currency-btn').addEventListener('click', async () => {
            const filePaths = await ipcRenderer.invoke('select-file', {
                properties: ['openFile'],
                filters: [
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
                ]
            });

            if (filePaths && filePaths.length > 0) {
                document.getElementById('currency-image-path').value = filePaths[0];
            }
        });

        // Sık kullanılanlar butonunu ayarla
        document.getElementById('favorites-btn').addEventListener('click', async () => {
            const dropdown = document.getElementById('favorites-dropdown');
            if (dropdown.style.display === 'none') {
                // Sık kullanılanları yükle
                const favorites = await ipcRenderer.invoke('load-favorites');
                const favoritesList = document.getElementById('favorites-list');
                favoritesList.innerHTML = '';

                if (favorites.length === 0) {
                    favoritesList.innerHTML = `<p style="text-align: center; color: #666;">${translations[currentLang].noFavorites}</p>`;
                } else {
                    favorites.forEach((favorite, index) => {
                        const item = document.createElement('div');
                        item.style.padding = '8px';
                        item.style.borderBottom = '1px solid #eee';
                        item.style.cursor = 'pointer';
                        item.style.display = 'flex';
                        item.style.justifyContent = 'space-between';
                        item.style.alignItems = 'center';

                        const titleElement = document.createElement('strong');
                        titleElement.textContent = favorite.title;
                        titleElement.style.flexGrow = '1';
                        titleElement.style.marginRight = '10px';

                        const deleteBtn = document.createElement('button');
                        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                        deleteBtn.style.padding = '3px 6px';
                        deleteBtn.style.backgroundColor = '#e74c3c';
                        deleteBtn.style.color = 'white';
                        deleteBtn.style.border = 'none';
                        deleteBtn.style.borderRadius = '3px';
                        deleteBtn.style.cursor = 'pointer';

                        // Silme butonu tıklandığında
                        deleteBtn.addEventListener('click', async (e) => {
                            e.stopPropagation(); // Üst elementin click olayını tetiklemeyi önle
                            const result = await ipcRenderer.invoke('delete-favorite', index);
                            if (result.success) {
                                // Favoriler listesini güncelle
                                item.remove();
                                if (result.favorites.length === 0) {
                                    favoritesList.innerHTML = `<p style="text-align: center; color: #666;">${translations[currentLang].noFavorites}</p>`;
                                }
                            } else {
                                alert(translations[currentLang].errorMsg + result.error);
                            }
                        });

                        item.appendChild(titleElement);
                        item.appendChild(deleteBtn);

                        item.addEventListener('mouseover', () => {
                            item.style.backgroundColor = '#f5f5f5';
                        });
                        item.addEventListener('mouseout', () => {
                            item.style.backgroundColor = 'transparent';
                        });
                        item.addEventListener('click', () => {
                            document.getElementById('tracking-code').value = favorite.code;
                            dropdown.style.display = 'none';
                        });
                        favoritesList.appendChild(item);
                    });
                }

                dropdown.style.display = 'block';

                // Dışarı tıklandığında dropdown'u kapat
                const closeDropdown = (e) => {
                    if (!dropdown.contains(e.target) && e.target !== document.getElementById('favorites-btn')) {
                        dropdown.style.display = 'none';
                        document.removeEventListener('click', closeDropdown);
                    }
                };

                // Bir sonraki tıklamada olay dinleyicisini ekle
                setTimeout(() => {
                    document.addEventListener('click', closeDropdown);
                }, 0);
            } else {
                dropdown.style.display = 'none';
            }
        });

        // Tracking kodunu kaydetme butonu
        document.getElementById('save-tracking-btn').addEventListener('click', async function() {
            const trackingCode = document.getElementById('tracking-code').value.trim();

            if (!trackingCode) {
                alert(translations[currentLang].enterTrackingCode);
                return;
            }

            // Electron'da prompt() desteklenmiyor, bu yüzden kendi dialog'umuzu oluşturalım
            const titleInput = document.createElement('div');
            titleInput.innerHTML = `
                <div style="position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center;">
                    <div style="background-color: white; padding: 20px; border-radius: 5px; width: 300px;">
                        <h3 style="margin-top: 0;">${translations[currentLang].favoriteTitle}</h3>
                        <input type="text" id="title-input" style="width: 100%; padding: 8px; margin-bottom: 15px; box-sizing: border-box;">
                        <div style="text-align: right;">
                            <button id="cancel-btn" style="padding: 8px 15px; margin-right: 10px; background-color: #f1f1f1; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Vazgeç</button>
                            <button id="save-btn" style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Kaydet</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(titleInput);

            // Input'a odaklan
            setTimeout(() => {
                document.getElementById('title-input').focus();
            }, 100);

            // Promise ile dialog'un sonuçlarını bekle
            const dialogPromise = new Promise((resolve, reject) => {
                document.getElementById('save-btn').addEventListener('click', () => {
                    const title = document.getElementById('title-input').value.trim();
                    if (title) {
                        resolve(title);
                    } else {
                        resolve(null);
                    }
                    document.body.removeChild(titleInput);
                });

                document.getElementById('cancel-btn').addEventListener('click', () => {
                    resolve(null);
                    document.body.removeChild(titleInput);
                });
            });

            // Dialog sonuçlarını bekle
            const title = await dialogPromise;
            if (title) {
                    try {
                        await ipcRenderer.invoke('save-favorite', {
                            title: title.trim(),
                            code: trackingCode
                        });
                        alert(translations[currentLang].trackingSaved);
                    } catch (error) {
                        console.error('Kaydetme hatası:', error);
                        alert(translations[currentLang].trackingSaveError);
                    }
                }
        });

        // Sürükle bırak işlemleri için fonksiyonlar
        function setupDragAndDrop(dropAreaId, inputId) {
            const dropArea = document.getElementById(dropAreaId);
            const input = document.getElementById(inputId);

            // Sürükleme olayları
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // Sürükleme sırasında stil değişiklikleri
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, () => {
                    dropArea.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, () => {
                    dropArea.classList.remove('drag-over');
                }, false);
            });

            // Dosya bırakıldığında
            dropArea.addEventListener('drop', async (e) => {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    // Sadece resim dosyalarını kabul et
                    if (file.type.match('image.*')) {
                        input.value = file.path;
                    }
                }
            }, false);
        }

        // Sürükle bırak alanlarını ayarla
        setupDragAndDrop('logo-drop-area', 'logo-image-path');
        setupDragAndDrop('bg-drop-area', 'bg-image-path');
        setupDragAndDrop('currency-drop-area', 'currency-image-path');

        // Ortak form doğrulama fonksiyonu
        function validateForm() {
            const gameName = document.getElementById('game-name').value.trim();
            const currency = document.getElementById('currency').value.trim();
            const cpaLink = document.getElementById('cpa-link').value.trim();
            const logoImage = document.getElementById('logo-image-path').value;
            const bgImage = document.getElementById('bg-image-path').value;
            const currencyImage = document.getElementById('currency-image-path').value;
            const trackingCode = document.getElementById('tracking-code').value.trim();
            const twitterUrl = document.getElementById('twitter-url').value.trim();
            const youtubeUrl = document.getElementById('youtube-url').value.trim();

            // Zorunlu alanları kontrol et
            if (!gameName || !currency || !cpaLink || !logoImage || !bgImage || !currencyImage) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = translations[currentLang].validationError;
                statusElement.className = 'status error';
                return null;
            }

            // URL formatlarını kontrol et
            if (cpaLink && !isValidUrl(cpaLink)) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = currentLang === 'tr' ? 'Geçerli bir CPA linki girin!' : 'Please enter a valid CPA link!';
                statusElement.className = 'status error';
                return null;
            }

            if (twitterUrl && !isValidUrl(twitterUrl)) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = currentLang === 'tr' ? 'Geçerli bir Twitter URL\'si girin!' : 'Please enter a valid Twitter URL!';
                statusElement.className = 'status error';
                return null;
            }

            if (youtubeUrl && !isValidUrl(youtubeUrl)) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = currentLang === 'tr' ? 'Geçerli bir YouTube URL\'si girin!' : 'Please enter a valid YouTube URL!';
                statusElement.className = 'status error';
                return null;
            }

            return {
                gameName,
                currency,
                cpaLink,
                logoImage,
                bgImage,
                currencyImage,
                trackingCode,
                twitterUrl,
                youtubeUrl
            };
        }

        // URL doğrulama fonksiyonu
        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        // Landing page oluşturma
        document.getElementById('create-btn').addEventListener('click', async () => {
            const formData = validateForm();
            if (!formData) return;

            // Landing page oluştur
            const result = await ipcRenderer.invoke('create-landing', formData);

            const statusElement = document.getElementById('status');

            if (result.success) {
                statusElement.textContent = translations[currentLang].successMsg;
                statusElement.className = 'status success';
                // Landing page listesini güncelle
                refreshLandingPagesList();
            } else {
                statusElement.textContent = translations[currentLang].errorMsg + result.error;
                statusElement.className = 'status error';
            }
        });

        // Landing page'leri listele
        async function refreshLandingPagesList() {
            const landingPagesList = document.getElementById('landing-pages-list');
            const result = await ipcRenderer.invoke('list-landing-pages');

            if (result.success) {
                if (result.landingPages.length === 0) {
                    landingPagesList.innerHTML = `<p style="text-align: center; padding: 20px; color: #666;">${translations[currentLang].noLandingPages}</p>`;
                } else {
                    landingPagesList.innerHTML = '';
                    result.landingPages.forEach(landingPage => {
                        const item = document.createElement('div');
                        item.style.padding = '10px';
                        item.style.borderBottom = '1px solid #eee';
                        item.style.display = 'flex';
                        item.style.justifyContent = 'space-between';
                        item.style.alignItems = 'center';

                        const nameElement = document.createElement('span');
                        nameElement.textContent = landingPage.name;
                        nameElement.style.fontWeight = 'bold';
                        nameElement.style.flexGrow = '1';

                        const buttonsContainer = document.createElement('div');
                        buttonsContainer.style.display = 'flex';
                        buttonsContainer.style.gap = '5px';

                        // Klasörü aç butonu
                        const openBtn = document.createElement('button');
                        openBtn.innerHTML = '<i class="fas fa-folder-open"></i>';
                        openBtn.style.padding = '5px 8px';
                        openBtn.style.backgroundColor = '#3498db';
                        openBtn.style.color = 'white';
                        openBtn.style.border = 'none';
                        openBtn.style.borderRadius = '3px';
                        openBtn.style.cursor = 'pointer';
                        openBtn.title = translations[currentLang].openFolderBtn;

                        openBtn.addEventListener('click', async () => {
                            await ipcRenderer.invoke('open-folder', landingPage.path);
                        });

                        // Sil butonu
                        const deleteBtn = document.createElement('button');
                        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                        deleteBtn.style.padding = '5px 8px';
                        deleteBtn.style.backgroundColor = '#e74c3c';
                        deleteBtn.style.color = 'white';
                        deleteBtn.style.border = 'none';
                        deleteBtn.style.borderRadius = '3px';
                        deleteBtn.style.cursor = 'pointer';
                        deleteBtn.title = translations[currentLang].deleteBtn;

                        deleteBtn.addEventListener('click', async () => {
                            if (confirm(translations[currentLang].deleteLandingConfirm)) {
                                const deleteResult = await ipcRenderer.invoke('delete-landing-page', landingPage.path);
                                if (deleteResult.success) {
                                    alert(translations[currentLang].landingPageDeleted);
                                    refreshLandingPagesList();
                                } else {
                                    alert(translations[currentLang].errorMsg + deleteResult.error);
                                }
                            }
                        });

                        buttonsContainer.appendChild(openBtn);
                        buttonsContainer.appendChild(deleteBtn);

                        item.appendChild(nameElement);
                        item.appendChild(buttonsContainer);

                        landingPagesList.appendChild(item);
                    });
                }
            } else {
                landingPagesList.innerHTML = `<p style="text-align: center; padding: 20px; color: #e74c3c;">${translations[currentLang].errorMsg}${result.error}</p>`;
            }
        }

        // Landing page'leri zipleme
        document.getElementById('zip-btn').addEventListener('click', async () => {
            const formData = validateForm();
            if (!formData) return;

            // Landing page oluştur ve ziple
            const result = await ipcRenderer.invoke('create-landing-zip', formData);

            const statusElement = document.getElementById('status');

            if (result.success) {
                statusElement.textContent = translations[currentLang].zipSuccessMsg;
                statusElement.className = 'status success';
                // Landing page listesini güncelle
                refreshLandingPagesList();
            } else {
                statusElement.textContent = translations[currentLang].errorMsg + result.error;
                statusElement.className = 'status error';
            }
        });

        // Sayfa yüklendiğinde landing page'leri listele
        refreshLandingPagesList();

        // Landing page listesini yenileme için IPC dinleyicisi
        ipcRenderer.on('refresh-landing-pages', () => {
            refreshLandingPagesList();
        });

    </script>
</body>
</html>
