// Test script to create a landing page and verify the Twitter/YouTube URL functionality
const { createLanding } = require('./main.js');

// Test data
const testData = {
  gameName: 'Test Game',
  currency: 'coin',
  cpaLink: 'https://example.com/test-cpa',
  twitterUrl: 'https://x.com/intent/post?text=Custom%20Twitter%20Message',
  youtubeUrl: 'https://www.youtube.com/custom-channel',
  logoImage: './template/images/logo.jpg',
  bgImage: './template/images/bg.jpg',
  currencyImage: './template/images/1.png',
  trackingCode: ''
};

console.log('Creating test landing page...');
console.log('Test data:', testData);

// This would be called by the main process
// createLanding(testData).then(result => {
//   console.log('Result:', result);
// });
