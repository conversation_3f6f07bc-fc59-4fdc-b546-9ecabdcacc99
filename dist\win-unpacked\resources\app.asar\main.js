const { app, <PERSON><PERSON>erWindow, ipc<PERSON>ain, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const archiver = require('archiver');
const rimraf = require('rimraf').sync;
const appDataPath = path.join(os.homedir(), '.landing-app-data');

// Packaged app için doğru path'i al
const isDev = !app.isPackaged;
let appPath;

if (isDev) {
  appPath = __dirname;
} else {
  // Production modunda farklı path stratejileri dene
  const exePath = app.getPath('exe');
  const exeDir = path.dirname(exePath);

  // Önce exe'nin yanındaki klasörleri kontrol et
  if (fs.existsSync(path.join(exeDir, 'app'))) {
    appPath = exeDir;
  } else if (fs.existsSync(path.join(exeDir, 'resources', 'app'))) {
    appPath = path.join(exeDir, 'resources', 'app');
  } else if (fs.existsSync(path.join(exeDir, '..', 'app'))) {
    appPath = path.join(exeDir, '..');
  } else {
    // Son çare olarak exe'nin bulunduğu klasör
    appPath = exeDir;
  }
}

// Debug: Path bilgilerini logla
console.log('=== PATH DEBUG INFO ===');
console.log('isDev:', isDev);
console.log('appPath:', appPath);
console.log('app folder exists:', fs.existsSync(path.join(appPath, 'app')));
console.log('template folder exists:', fs.existsSync(path.join(appPath, 'template')));
console.log('=======================');

// Output klasörü için varsayılan yol
let outputPath = path.join(appPath, 'output');

// Output path'i kaydetmek için dosya yolu
const outputPathFile = path.join(appDataPath, 'output-path.json');

// Uygulama veri klasörünü oluştur
if (!fs.existsSync(appDataPath)) {
  fs.mkdirSync(appDataPath, { recursive: true });
}

// Kaydedilen output path'i yükle
function loadOutputPath() {
  try {
    if (fs.existsSync(outputPathFile)) {
      const data = fs.readFileSync(outputPathFile, 'utf8');
      const pathData = JSON.parse(data);
      if (pathData.outputPath && fs.existsSync(pathData.outputPath)) {
        outputPath = pathData.outputPath;
        console.log('Output path yüklendi:', outputPath);
      }
    }
  } catch (error) {
    console.error('Output path yükleme hatası:', error);
  }
}

// Kaydedilen output path'i kaydet
function saveOutputPath(newPath) {
  try {
    fs.writeFileSync(outputPathFile, JSON.stringify({ outputPath: newPath }), 'utf8');
    console.log('Output path kaydedildi:', newPath);
  } catch (error) {
    console.error('Output path kaydetme hatası:', error);
  }
}

// Başlangıçta output path'i yükle
loadOutputPath();

// Sık kullanılan tracking kodları için dosya yolu
const favoritesFilePath = path.join(appDataPath, 'favorites.json');

let mainWindow;

// Metni ilk harfleri büyük olacak şekilde formatını düzenler
function formatNameWithCapitals(text) {
  if (!text) return text;

  // Metni boşluklara göre böl
  return text.split(' ')
    // Her kelimenin ilk harfini büyük, geri kalanını küçük yap
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    // Kelimeleri tekrar birleştir
    .join(' ');
}

// Eski referansları tespit eden ve raporlayan fonksiyon
function detectOldReferences(content, oldGameName, oldCurrency) {
  const issues = [];

  // Oyun adı referanslarını kontrol et
  const gameNameRegex = new RegExp(`\\b${oldGameName.replace(/\s+/g, '\\s+')}\\b`, 'gi');
  const gameNameMatches = content.match(gameNameRegex) || [];

  // Tire ile ayrılmış oyun adı referanslarını kontrol et
  const dashedGameNameRegex = new RegExp(`\\b${oldGameName.toLowerCase().replace(/\s+/g, '[\\-_]')}\\b`, 'gi');
  const dashedGameNameMatches = content.match(dashedGameNameRegex) || [];

  // Birleşik yazılmış oyun adı referanslarını kontrol et
  const combinedGameNameRegex = new RegExp(`\\b${oldGameName.toLowerCase().replace(/\s+/g, '')}\\b`, 'gi');
  const combinedGameNameMatches = content.match(combinedGameNameRegex) || [];

  // Para birimi referanslarını kontrol et
  const currencyRegex = new RegExp(`\\b${oldCurrency}\\b`, 'gi');
  const currencyMatches = content.match(currencyRegex) || [];

  // URL referanslarını kontrol et
  const urlRegex = new RegExp(`https?:\/\/[^\"'\s)]*${oldGameName.toLowerCase().replace(/\s+/g, '')}[^\"'\s]*`, 'gi');
  const urlMatches = content.match(urlRegex) || [];

  // Bulunan tüm eşleşmeleri issues dizisine ekle
  if (gameNameMatches.length > 0) {
    issues.push({
      type: 'gameNameDirect',
      matches: gameNameMatches,
      count: gameNameMatches.length
    });
  }

  if (dashedGameNameMatches.length > 0) {
    issues.push({
      type: 'gameNameDashed',
      matches: dashedGameNameMatches,
      count: dashedGameNameMatches.length
    });
  }

  if (combinedGameNameMatches.length > 0) {
    issues.push({
      type: 'gameNameCombined',
      matches: combinedGameNameMatches,
      count: combinedGameNameMatches.length
    });
  }

  if (currencyMatches.length > 0) {
    issues.push({
      type: 'currency',
      matches: currencyMatches,
      count: currencyMatches.length
    });
  }

  if (urlMatches.length > 0) {
    issues.push({
      type: 'url',
      matches: urlMatches,
      count: urlMatches.length
    });
  }

  return issues;
}

// Gelişmiş metin değiştirme fonksiyonu
function advancedReplace(content, oldGameName, oldCurrency, newGameName, newCurrency, newCpaLink) {
  // Oyun adı için farklı varyasyonları değiştir

  // 1. Normal yazım (Brawl Stars)
  const gameNameRegex = new RegExp(`\\b${oldGameName}\\b`, 'g');
  content = content.replace(gameNameRegex, newGameName);

  // 2. Küçük harfli yazım (brawl stars)
  const lowerGameNameRegex = new RegExp(`\\b${oldGameName.toLowerCase()}\\b`, 'g');
  content = content.replace(lowerGameNameRegex, newGameName.toLowerCase());

  // 3. Büyük harfli yazım (BRAWL STARS)
  const upperGameNameRegex = new RegExp(`\\b${oldGameName.toUpperCase()}\\b`, 'g');
  content = content.replace(upperGameNameRegex, newGameName.toUpperCase());

  // 4. Tire ile ayrılmış yazım (brawl-stars)
  const dashedGameNameRegex = new RegExp(`\\b${oldGameName.toLowerCase().replace(/\s+/g, '[\\-_]')}\\b`, 'g');
  content = content.replace(dashedGameNameRegex, newGameName.toLowerCase().replace(/\s+/g, '-'));

  // 5. Birleşik yazım (brawlstars)
  const combinedGameNameRegex = new RegExp(`\\b${oldGameName.toLowerCase().replace(/\s+/g, '')}\\b`, 'g');
  content = content.replace(combinedGameNameRegex, newGameName.toLowerCase().replace(/\s+/g, ''));

  // Para birimi için farklı varyasyonları değiştir

  // 1. Normal yazım (elmas)
  const currencyRegex = new RegExp(`\\b${oldCurrency}\\b`, 'g');
  content = content.replace(currencyRegex, newCurrency);

  // 2. Büyük harfle başlayan yazım (Elmas)
  const capitalCurrencyRegex = new RegExp(`\\b${oldCurrency.charAt(0).toUpperCase() + oldCurrency.slice(1)}\\b`, 'g');
  content = content.replace(capitalCurrencyRegex, newCurrency.charAt(0).toUpperCase() + newCurrency.slice(1));

  // 3. Tümü büyük harfli yazım (ELMAS)
  const upperCurrencyRegex = new RegExp(`\\b${oldCurrency.toUpperCase()}\\b`, 'g');
  content = content.replace(upperCurrencyRegex, newCurrency.toUpperCase());

  // 4. Çoğul yazım (elmaslar)
  const pluralCurrencyRegex = new RegExp(`\\b${oldCurrency}lar\\b`, 'g');
  content = content.replace(pluralCurrencyRegex, `${newCurrency}lar`);

  // 5. Büyük harfle başlayan çoğul yazım (Elmaslar)
  const capitalPluralCurrencyRegex = new RegExp(`\\b${oldCurrency.charAt(0).toUpperCase() + oldCurrency.slice(1)}lar\\b`, 'g');
  content = content.replace(capitalPluralCurrencyRegex, `${newCurrency.charAt(0).toUpperCase() + newCurrency.slice(1)}lar`);

  // 6. Tümü büyük harfli çoğul yazım (ELMASLAR)
  const upperPluralCurrencyRegex = new RegExp(`\\b${oldCurrency.toUpperCase()}LAR\\b`, 'g');
  content = content.replace(upperPluralCurrencyRegex, `${newCurrency.toUpperCase()}LAR`);

  // URL'leri değiştir - BASİTLEŞTİRİLMİŞ

  // 1. Sabit CPA link değerlerini değiştir
  content = content.replace(/https:\/\/example\.com\/cpa-link/g, newCpaLink);

  // 2. script.min.js içindeki verify-button href'ini değiştir
  content = content.replace(/document\.getElementById\('verify-button'\)\.href="[^"]*"/g,
    `document.getElementById('verify-button').href="${newCpaLink}"`);

  // 3. Sosyal paylaşım butonları için sabit değerler
  // Facebook
  content = content.replace(/href="https:\/\/www\.facebook\.com\/sharer\/sharer\.php\?u=[^"]*"/g,
    `href="https://www.facebook.com/sharer/sharer.php?u=https://cdnjs.cloudflare.com"`);

  // Twitter
  content = content.replace(/href="https:\/\/twitter\.com\/intent\/tweet\?url=[^&]*&text=[^"]*"/g,
    `href="https://twitter.com/intent/tweet?url=https://cdnjs.cloudflare.com&text=${newGameName}%20${newCurrency}%20Hilesi"`);

  // WhatsApp
  content = content.replace(/href="https:\/\/wa\.me\/\?text=[^"]*"/g,
    `href="https://wa.me/?text=${newGameName}%20${newCurrency}%20Hilesi"`);

  // Telegram
  content = content.replace(/href="https:\/\/t\.me\/share\/url\?url=[^&]*&text=[^"]*"/g,
    `href="https://t.me/share/url?url=https://cdnjs.cloudflare.com&text=${newGameName}%20${newCurrency}%20Hilesi"`);

  // Email
  content = content.replace(/href="mailto:\?subject=[^&]*&body=[^"]*"/g,
    `href="mailto:?subject=${newGameName}%20${newCurrency}%20Hilesi&body=${newGameName}%20${newCurrency}%20Hilesi"`);

  // 4. Schema.org markup'taki URL'leri düzelt
  content = content.replace(/"url":\s*"[^"]*"/g, `"url": "${newCpaLink}"`);

  // 5. SearchAction target URL'sini düzelt
  content = content.replace(/"target":\s*"[^"]*"/g, `"target": "${newCpaLink}/search?q={search_term_string}"`);

  // 6. Sosyal medya paylaşım metinleri
  const shareTextRegex = new RegExp(`${oldGameName}[^\"'<>]*${oldCurrency}`, 'gi');
  content = content.replace(shareTextRegex, `${newGameName} ${newCurrency}`);


  // Cache adı gibi özel durumlar
  const cacheNameRegex = new RegExp(`${oldGameName.toLowerCase().replace(/\s+/g, '-')}-${oldCurrency.toLowerCase()}-hilesi-v1`, 'g');
  content = content.replace(cacheNameRegex, `${newGameName.toLowerCase().replace(/\s+/g, '-')}-${newCurrency.toLowerCase()}-hilesi-v1`);

  return content;
}

// Menü oluşturma fonksiyonu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Path',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openDirectory'],
              title: 'Select Output Directory'
            });

            if (!result.canceled && result.filePaths.length > 0) {
              const selectedPath = result.filePaths[0];
              outputPath = selectedPath;
              saveOutputPath(selectedPath);

              // Landing page listesini güncelle
              mainWindow.webContents.send('refresh-landing-pages');
            }
          }
        },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'toggledevtools' },
        { type: 'separator' },
        { role: 'resetzoom' },
        { role: 'zoomin' },
        { role: 'zoomout' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: true
    }
  });

  mainWindow.loadFile(path.join(appPath, 'app', 'index.html'));

  // Geliştirme aşamasında DevTools'u açmak için
  // mainWindow.webContents.openDevTools();

  mainWindow.on('closed', function () {
    mainWindow = null;
  });

  // Menüyü oluştur
  createMenu();
}

app.on('ready', createWindow);

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function () {
  if (mainWindow === null) createWindow();
});

// Dosya seçme diyaloğu
ipcMain.handle('select-file', async (_, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result.filePaths;
});

// Sık kullanılan tracking kodlarını yükleme
ipcMain.handle('load-favorites', async () => {
  try {
    if (fs.existsSync(favoritesFilePath)) {
      const favoritesData = fs.readFileSync(favoritesFilePath, 'utf8');
      return JSON.parse(favoritesData);
    }
    return [];
  } catch (error) {
    console.error('Sık kullanılanları yükleme hatası:', error);
    return [];
  }
});

// Başlık girişi için input dialog gösterme
ipcMain.handle('show-input-dialog', async (_, options) => {
  try {
    const result = await dialog.showMessageBox(mainWindow, {
      title: options.title || 'Başlık Girin',
      message: options.message || 'Bu kod için bir başlık girin:',
      buttons: ['Vazgeç', 'Kaydet'],
      defaultId: 1,
      cancelId: 0,
      prompt: true,
      inputValue: options.defaultValue || '',
    });

    if (result.response === 1 && result.checkboxChecked) {
      return { success: true, value: result.checkboxChecked };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error('Input dialog hatası:', error);
    return { success: false, error: error.message };
  }
});

// Başlık giriş diyaloğu gösterme
ipcMain.handle('show-title-dialog', async (_, message) => {
  try {
    console.log('show-title-dialog çağrıldı');
    const result = await dialog.showMessageBox(mainWindow, {
      title: 'Başlık Girin',
      message: message,
      buttons: ['Vazgeç', 'Kaydet'],
      defaultId: 1,
      cancelId: 0,
      promptForInput: true, // Input alanı göster
    });

    console.log('Dialog sonucu:', result);

    if (result.response === 1 && result.promptInput) {
      return { success: true, title: result.promptInput.trim() };
    } else {
      return { success: false };
    }
  } catch (error) {
    console.error('Başlık diyaloğu hatası:', error);
    return { success: false, error: error.message };
  }
});

// Sık kullanılan tracking kodunu kaydetme
ipcMain.handle('save-favorite', async (_, favorite) => {
  console.log('save-favorite çağrıldı:', favorite);
  try {
    let favorites = [];
    if (fs.existsSync(favoritesFilePath)) {
      const favoritesData = fs.readFileSync(favoritesFilePath, 'utf8');
      favorites = JSON.parse(favoritesData);
      console.log('Mevcut favoriler yüklendi, sayısı:', favorites.length);
    } else {
      console.log('Favoriler dosyası bulunamadı, yeni oluşturulacak');
    }

    favorites.push(favorite);
    fs.writeFileSync(favoritesFilePath, JSON.stringify(favorites, null, 2), 'utf8');
    console.log('Favori başarıyla kaydedildi');
    return { success: true };
  } catch (error) {
    console.error('Sık kullanılan kaydetme hatası:', error);
    return { success: false, error: error.message };
  }
});

// Sık kullanılan tracking kodunu silme
ipcMain.handle('delete-favorite', async (_, index) => {
  console.log('delete-favorite çağrıldı, index:', index);
  try {
    if (fs.existsSync(favoritesFilePath)) {
      const favoritesData = fs.readFileSync(favoritesFilePath, 'utf8');
      let favorites = JSON.parse(favoritesData);

      if (index >= 0 && index < favorites.length) {
        // Belirtilen indeksteki favoriyi sil
        favorites.splice(index, 1);
        fs.writeFileSync(favoritesFilePath, JSON.stringify(favorites, null, 2), 'utf8');
        console.log('Favori başarıyla silindi');
        return { success: true, favorites };
      } else {
        return { success: false, error: 'Geçersiz indeks' };
      }
    } else {
      return { success: false, error: 'Favoriler dosyası bulunamadı' };
    }
  } catch (error) {
    console.error('Sık kullanılan silme hatası:', error);
    return { success: false, error: error.message };
  }
});

// Oluşturulan landing page'leri listeleme
ipcMain.handle('list-landing-pages', async () => {
  try {
    const outputDir = outputPath;

    // Output klasörü yoksa oluştur
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      return { success: true, landingPages: [] };
    }

    // Output klasöründeki alt klasörleri al
    const items = fs.readdirSync(outputDir, { withFileTypes: true });
    const landingPages = items
      .filter(item => item.isDirectory())
      .map(item => {
        const fullPath = path.join(outputDir, item.name);
        return {
          name: item.name,
          path: fullPath
        };
      });

    return { success: true, landingPages };
  } catch (error) {
    console.error('Landing page listeleme hatası:', error);
    return { success: false, error: error.message };
  }
});

// Landing page silme
ipcMain.handle('delete-landing-page', async (_, landingPagePath) => {
  try {
    if (fs.existsSync(landingPagePath)) {
      // rimraf kullanarak klasörü sil (daha güçlü silme işlemi)
      rimraf(landingPagePath);
      console.log('Landing page başarıyla silindi:', landingPagePath);
      return { success: true };
    } else {
      return { success: false, error: 'Landing page bulunamadı' };
    }
  } catch (error) {
    console.error('Landing page silme hatası:', error);
    return { success: false, error: error.message };
  }
});

// Klasörü açma
ipcMain.handle('open-folder', async (_, folderPath) => {
  try {
    if (fs.existsSync(folderPath)) {
      // Windows'ta klasörü aç
      require('child_process').exec(`start "" "${folderPath}"`);
      return { success: true };
    } else {
      return { success: false, error: 'Klasör bulunamadı' };
    }
  } catch (error) {
    console.error('Klasör açma hatası:', error);
    return { success: false, error: error.message };
  }
});



// Landing page oluşturma fonksiyonu
async function createLanding(data) {
  try {
    // Oyun adı ve para birimini formatını düzenle (ilk harfler büyük)
    data.gameName = formatNameWithCapitals(data.gameName);
    data.currency = formatNameWithCapitals(data.currency);

    // Template klasörünün yolu
    const templateDir = path.join(appPath, 'template');

    // Ana output klasörü
    const mainOutputDir = outputPath;

    // Eğer ana output klasörü yoksa oluştur
    if (!fs.existsSync(mainOutputDir)) {
      fs.mkdirSync(mainOutputDir, { recursive: true });
    }

    // Oyun adına göre output klasörü oluştur
    const gameNameSafe = data.gameName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const outputDir = path.join(mainOutputDir, gameNameSafe);

    // Oyun klasörünü temizle (varsa)
    if (fs.existsSync(outputDir)) {
      // rimraf kullanarak klasörü sil (daha güçlü silme işlemi)
      rimraf(outputDir);
    }

    // Oyun klasörünü oluştur
    fs.mkdirSync(outputDir, { recursive: true });

    // Template klasörünü kopyala
    copyFolderRecursiveSync(templateDir, outputDir);

    // HTML dosyasını güncelle
    updateHtmlFile(data, outputDir, data.twitterUrl);

    // CSS dosyasını güncelle
    updateCssFile(data, outputDir);

    // JS dosyasını güncelle
    updateJsFile(data, outputDir);

    // Service Worker dosyasını güncelle
    updateServiceWorkerFile(data, outputDir);

    // Resimleri kopyala
    copyImages(data, outputDir);

    // README.md dosyasını güncelle
    updateReadmeFile(data, outputDir);

    return { success: true, outputDir };
  } catch (error) {
    console.error('Hata:', error);
    return { success: false, error: error.message };
  }
}

// Landing page oluşturma işlemi
ipcMain.handle('create-landing', async (_, data) => {
  // createLanding fonksiyonunu çağır
  return await createLanding(data);
});

// Klasörü recursive olarak kopyalama
function copyFolderRecursiveSync(source, target) {
  // Hedef klasörü oluştur
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  // Kaynak bir dizin ise
  if (fs.lstatSync(source).isDirectory()) {
    // Kaynak dizindeki tüm dosyaları al
    const files = fs.readdirSync(source);

    // Her dosya için işlem yap
    files.forEach(function (file) {
      const curSource = path.join(source, file);
      const curTarget = path.join(target, file);

      // Eğer dosya bir dizin ise, recursive olarak kopyala
      if (fs.lstatSync(curSource).isDirectory()) {
        // Önce hedef dizini oluştur
        if (!fs.existsSync(curTarget)) {
          fs.mkdirSync(curTarget, { recursive: true });
        }
        // Sonra içeriğini kopyala
        copyFolderRecursiveSync(curSource, curTarget);
      }
      // Dosya ise doğrudan kopyala
      else {
        fs.copyFileSync(curSource, curTarget);
      }
    });
  }
}

// HTML dosyasını güncelleme
function updateHtmlFile(data, outputDir, twitterUrl) {
  // HTML dosyasının yolunu kontrol et
  const htmlFilePath = path.join(outputDir, 'index.html');

  // Dosya yoksa, template klasöründeki index.html'i kontrol et
  if (!fs.existsSync(htmlFilePath)) {
    console.log('index.html bulunamadı, template klasöründeki dosyayı kontrol ediyorum...');
    const templateHtmlPath = path.join(appPath, 'template', 'index.html');

    if (fs.existsSync(templateHtmlPath)) {
      // Template'den output klasörüne kopyala
      fs.copyFileSync(templateHtmlPath, htmlFilePath);
      console.log('index.html dosyası kopyalandı.');
    } else {
      throw new Error('Template klasöründe index.html bulunamadı!');
    }
  }

  let htmlContent = fs.readFileSync(htmlFilePath, 'utf8');

  // Gelişmiş metin değiştirme fonksiyonunu kullan
  htmlContent = advancedReplace(htmlContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

  // Twitter ve YouTube URL placeholder'larını değiştir
  htmlContent = htmlContent.replace(/TWITTER_URL_PLACEHOLDER/g, data.twitterUrl || 'https://x.com/intent/post?text=%20Olmaz%20diyordum%20ama%20oldu.%20eFootball%20Bedava%20Paralar%C4%B1m%C4%B1%20Ald%C4%B1m.demek%20millet%20bu%20y%C3%BCzden%20bu%20kadar%20fark%20a%C3%A7%C4%B1yormu%C5%9F..%20neyse%20ge%C3%A7%20olsun%20g%C3%BC%C3%A7%20olmas%C4%B1n%20haha.%20eFootball%20Bedava%20Paralar%C4%B1n%C4%B1z%C4%B1%20sizde%20al%C4%B1n%20bitmeden%20&url=%20cutt.ly%2F28IN1mF');
  htmlContent = htmlContent.replace(/YOUTUBE_URL_PLACEHOLDER/g, data.youtubeUrl || 'https://www.youtube.com/@senmakiraz4690/channels');

  // Sosyal paylaşım butonlarını özel olarak güncelle
  htmlContent = updateSocialShareButtons(htmlContent, data.gameName, data.currency, data.cpaLink, twitterUrl);

  // Schema.org markup'ını özel olarak güncelle
  htmlContent = updateSchemaMarkup(htmlContent, data.gameName, data.currency, data.cpaLink);

  // Canonical URL ve meta etiketlerini güncelle
  htmlContent = htmlContent.replace(/<link rel="canonical"[^>]*>/g,
    `<link rel="canonical" href="${data.cpaLink}">`);

  // Open Graph URL'lerini güncelle
  htmlContent = htmlContent.replace(/<meta property="og:url"[^>]*>/g,
    `<meta property="og:url" content="${data.cpaLink}">`);

  // Open Graph title ve description güncelle
  htmlContent = htmlContent.replace(/<meta property="og:title"[^>]*>/g,
    `<meta property="og:title" content="${data.gameName} ${data.currency.charAt(0).toUpperCase() + data.currency.slice(1)} Hilesi">`);

  htmlContent = htmlContent.replace(/<meta property="og:description"[^>]*>/g,
    `<meta property="og:description" content="Ücretsiz ${data.gameName} ${data.currency} kazan">`);

  // Tracking kodunu ekle (varsa)
  if (data.trackingCode && data.trackingCode.trim() !== '') {
    const trackingCodeScript = `
    <!-- Tracking Code -->
    ${data.trackingCode}`;

    // Tracking kodunu </head> etiketinden önce ekle
    htmlContent = htmlContent.replace('</head>', trackingCodeScript + '\n</head>');
  }

  fs.writeFileSync(htmlFilePath, htmlContent, 'utf8');

  // Eski referansları tespit et ve raporla
  const issues = detectOldReferences(htmlContent, 'Brawl Stars', 'elmas');
  if (issues.length > 0) {
    console.log('\n\nHTML dosyasında potansiyel eski referanslar tespit edildi:');
    issues.forEach(issue => {
      console.log(`- ${issue.type} tipinde ${issue.count} adet eşleşme bulundu.`);
      if (issue.matches.length > 0) {
        console.log('  Örnekler: ' + issue.matches.slice(0, 3).join(', '));
      }
    });
  }
}

// Sosyal paylaşım butonlarını güncelleme
function updateSocialShareButtons(content, gameName, currency, cpaLink, twitterUrl) {
  // Facebook paylaşım butonunu güncelle
  content = content.replace(/href="https:\/\/www\.facebook\.com\/sharer\/sharer\.php\?u=[^"]*"/g,
    `href="https://www.facebook.com/sharer/sharer.php?u=${cpaLink}"`);

  // Twitter paylaşım butonunu güncelle
  content = content.replace(/href="https:\/\/twitter\.com\/intent\/tweet\?url=[^&]*&text=[^"]*"/g,
    `href="${twitterUrl}"`);

  // WhatsApp paylaşım butonunu güncelle
  const whatsappText = `${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi ile ücretsiz ${currency} kazanın!`;
  content = content.replace(/href="https:\/\/wa\.me\/\?text=[^"]*"/g,
    `href="https://wa.me/?text=${encodeURIComponent(whatsappText + ' ' + cpaLink)}"`);

  // Telegram paylaşım butonunu güncelle
  const telegramText = `${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi ile ücretsiz ${currency} kazanın!`;
  content = content.replace(/href="https:\/\/t\.me\/share\/url\?url=[^&]*&text=[^"]*"/g,
    `href="https://t.me/share/url?url=${cpaLink}&text=${encodeURIComponent(telegramText)}"`);

  // Email paylaşım butonunu güncelle
  const emailSubject = `${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi`;
  const emailBody = `${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi ile ücretsiz ${currency} kazanın!`;
  content = content.replace(/href="mailto:\?subject=[^&]*&body=[^"]*"/g,
    `href="mailto:?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody + ' ' + cpaLink)}"`);

  return content;
}

// Schema.org markup'ını güncelleme
function updateSchemaMarkup(content, gameName, currency, cpaLink) {
  // Schema.org WebSite name güncelle
  const schemaNameRegex = /"name":\s*"[^"]*"/g;
  content = content.replace(schemaNameRegex, `"name": "${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi"`);

  // Schema.org URL güncelle
  const schemaUrlRegex = /"url":\s*"[^"]*"/g;
  content = content.replace(schemaUrlRegex, `"url": "${cpaLink}"`);

  // Schema.org description güncelle
  const schemaDescRegex = /"description":\s*"[^"]*"/g;
  content = content.replace(schemaDescRegex, `"description": "${gameName} ${currency.charAt(0).toUpperCase() + currency.slice(1)} Hilesi ile ücretsiz ${currency} kazanın. En güncel ve çalışan ${gameName} hilesi."`);

  // Schema.org SearchAction target güncelle
  const schemaSearchRegex = /"target":\s*"[^"]*"/g;
  content = content.replace(schemaSearchRegex, `"target": "${cpaLink}/search?q={search_term_string}"`);

  return content;
}

// CSS dosyasını güncelleme
function updateCssFile(data, outputDir) {
  // CSS klasörünü oluştur
  const cssDir = path.join(outputDir, 'css');
  if (!fs.existsSync(cssDir)) {
    fs.mkdirSync(cssDir, { recursive: true });
  }

  const cssFilePath = path.join(cssDir, 'style.css');
  const minCssFilePath = path.join(cssDir, 'style.min.css');

  // Template'deki CSS dosyalarını kontrol et
  const templateCssPath = path.join(appPath, 'template', 'css', 'style.css');
  const templateMinCssPath = path.join(appPath, 'template', 'css', 'style.min.css');

  // Normal CSS dosyasını kopyala ve güncelle
  if (fs.existsSync(templateCssPath)) {
    if (!fs.existsSync(cssFilePath)) {
      fs.copyFileSync(templateCssPath, cssFilePath);
    }

    let cssContent = fs.readFileSync(cssFilePath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    cssContent = advancedReplace(cssContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    fs.writeFileSync(cssFilePath, cssContent, 'utf8');

    // Eski referansları tespit et ve raporla
    const issues = detectOldReferences(cssContent, 'Brawl Stars', 'elmas');
    if (issues.length > 0) {
      console.log('\n\nCSS dosyasında potansiyel eski referanslar tespit edildi:');
      issues.forEach(issue => {
        console.log(`- ${issue.type} tipinde ${issue.count} adet eşleşme bulundu.`);
        if (issue.matches.length > 0) {
          console.log('  Örnekler: ' + issue.matches.slice(0, 3).join(', '));
        }
      });
    }
  }

  // Minify CSS dosyasını kopyala ve güncelle
  if (fs.existsSync(templateMinCssPath)) {
    if (!fs.existsSync(minCssFilePath)) {
      fs.copyFileSync(templateMinCssPath, minCssFilePath);
    }

    let minCssContent = fs.readFileSync(minCssFilePath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    minCssContent = advancedReplace(minCssContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    fs.writeFileSync(minCssFilePath, minCssContent, 'utf8');
  }
}

// JS dosyasını güncelleme
function updateJsFile(data, outputDir) {
  // JS klasörünü oluştur
  const jsDir = path.join(outputDir, 'js');
  if (!fs.existsSync(jsDir)) {
    fs.mkdirSync(jsDir, { recursive: true });
  }

  const jsFilePath = path.join(jsDir, 'script.js');
  const minJsFilePath = path.join(jsDir, 'script.min.js');

  // Template'deki JS dosyalarını kontrol et
  const templateJsPath = path.join(appPath, 'template', 'js', 'script.js');
  const templateMinJsPath = path.join(appPath, 'template', 'js', 'script.min.js');

  // Normal JS dosyasını kopyala
  if (fs.existsSync(templateJsPath)) {
    if (!fs.existsSync(jsFilePath)) {
      fs.copyFileSync(templateJsPath, jsFilePath);
    }

    // Template'deki JS dosyasının içeriğini oku
    let jsContent = fs.readFileSync(templateJsPath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    jsContent = advancedReplace(jsContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    // Oluşturulan JS dosyasına içeriği yaz
    fs.writeFileSync(jsFilePath, jsContent, 'utf8');
  }

  // Minify JS dosyasını kopyala
  if (fs.existsSync(templateMinJsPath)) {
    if (!fs.existsSync(minJsFilePath)) {
      fs.copyFileSync(templateMinJsPath, minJsFilePath);
    }

    // Template'deki Minify JS dosyasının içeriğini oku
    let minJsContent = fs.readFileSync(templateMinJsPath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    minJsContent = advancedReplace(minJsContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    // Oluşturulan Minify JS dosyasına içeriği yaz
    fs.writeFileSync(minJsFilePath, minJsContent, 'utf8');
  }
}

// Service Worker dosyasını güncelleme
function updateServiceWorkerFile(data, outputDir) {
  // Template'deki Service Worker dosyasını kontrol et
  const templateSwPath = path.join(appPath, 'template', 'service-worker.js');
  const swFilePath = path.join(outputDir, 'service-worker.js');

  if (fs.existsSync(templateSwPath)) {
    // Service Worker dosyasını kopyala
    if (!fs.existsSync(swFilePath)) {
      fs.copyFileSync(templateSwPath, swFilePath);
    }

    let swContent = fs.readFileSync(swFilePath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    swContent = advancedReplace(swContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    // Cache adını güncelle
    swContent = swContent.replace(/brawl-stars-elmas-hilesi-v1/g,
      `${data.gameName.toLowerCase().replace(/\s+/g, '-')}-${data.currency.toLowerCase()}-hilesi-v1`);

    fs.writeFileSync(swFilePath, swContent, 'utf8');

    // Eski referansları tespit et ve raporla
    const issues = detectOldReferences(swContent, 'Brawl Stars', 'elmas');
    if (issues.length > 0) {
      console.log('\n\nService Worker dosyasında potansiyel eski referanslar tespit edildi:');
      issues.forEach(issue => {
        console.log(`- ${issue.type} tipinde ${issue.count} adet eşleşme bulundu.`);
        if (issue.matches.length > 0) {
          console.log('  Örnekler: ' + issue.matches.slice(0, 3).join(', '));
        }
      });
    }
  }
}

// Resimleri kopyalama
function copyImages(data, outputDir) {
  // Images klasörünü oluştur
  const imagesDir = path.join(outputDir, 'images');
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  // Template'deki resim dosyalarının yollarını al
  const templateLogoPath = path.join(appPath, 'template', 'images', 'logo.jpg');
  const templateBgPath = path.join(appPath, 'template', 'images', 'bg.jpg');
  const templateCurrencyPath = path.join(appPath, 'template', 'images', '1.png');
  const templateVideoPath = path.join(appPath, 'template', 'images', '12.mp4');

  // Logo resmini kopyala - her zaman logo.jpg olarak kaydet
  if (data.logoImage && fs.existsSync(data.logoImage)) {
    fs.copyFileSync(data.logoImage, path.join(imagesDir, 'logo.jpg'));
  } else if (fs.existsSync(templateLogoPath)) {
    // Template'deki logo resmini kopyala
    fs.copyFileSync(templateLogoPath, path.join(imagesDir, 'logo.jpg'));
  }

  // Arkaplan resmini kopyala - her zaman bg.jpg olarak kaydet
  if (data.bgImage && fs.existsSync(data.bgImage)) {
    fs.copyFileSync(data.bgImage, path.join(imagesDir, 'bg.jpg'));
  } else if (fs.existsSync(templateBgPath)) {
    // Template'deki arkaplan resmini kopyala
    fs.copyFileSync(templateBgPath, path.join(imagesDir, 'bg.jpg'));
  }

  // Para birimi resmini kopyala - her zaman 1.png olarak kaydet
  if (data.currencyImage && fs.existsSync(data.currencyImage)) {
    fs.copyFileSync(data.currencyImage, path.join(imagesDir, '1.png'));
  } else if (fs.existsSync(templateCurrencyPath)) {
    // Template'deki para birimi resmini kopyala
    fs.copyFileSync(templateCurrencyPath, path.join(imagesDir, '1.png'));
  }

  // Video dosyasını kopyala
  if (fs.existsSync(templateVideoPath)) {
    fs.copyFileSync(templateVideoPath, path.join(imagesDir, '12.mp4'));
  }
}

// README.md dosyasını güncelleme
function updateReadmeFile(data, outputDir) {
  const readmeFilePath = path.join(outputDir, 'README.md');
  const templateReadmePath = path.join(appPath, 'template', 'README.md');

  // Template README.md dosyası varsa kopyala ve güncelle
  if (fs.existsSync(templateReadmePath)) {
    if (!fs.existsSync(readmeFilePath)) {
      fs.copyFileSync(templateReadmePath, readmeFilePath);
    }

    let readmeContent = fs.readFileSync(readmeFilePath, 'utf8');

    // Gelişmiş metin değiştirme fonksiyonunu kullan
    readmeContent = advancedReplace(readmeContent, 'Brawl Stars', 'elmas', data.gameName, data.currency, data.cpaLink);

    fs.writeFileSync(readmeFilePath, readmeContent, 'utf8');
  }
}

// Landing page oluşturma ve zipleme işlemi
ipcMain.handle('create-landing-zip', async (_, data) => {
  try {
    // Önce normal landing page oluştur
    // create-landing fonksiyonunu doğrudan çağır
    const result = await createLanding(data);

    if (!result.success) {
      return result; // Hata varsa doğrudan döndür
    }

    // Zip dosyası oluştur
    const outputDir = result.outputDir;
    const gameNameSafe = data.gameName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    // Zip dosyasını landing page klasörünün içine kaydet
    const zipFilePath = path.join(outputDir, `${gameNameSafe}.zip`);

    // Eğer aynı isimde zip dosyası varsa sil
    if (fs.existsSync(zipFilePath)) {
      fs.unlinkSync(zipFilePath);
    }

    // Zip işlemini başlat
    const output = fs.createWriteStream(zipFilePath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // En yüksek sıkıştırma seviyesi
    });

    // Zip işlemi tamamlandığında
    output.on('close', () => {
      console.log(`Zip dosyası oluşturuldu: ${zipFilePath}`);
      console.log(`Toplam boyut: ${archive.pointer()} byte`);
    });

    // Hata durumunda
    archive.on('error', (err) => {
      throw err;
    });

    // Zip dosyasına yazma işlemini başlat
    archive.pipe(output);

    // Klasörü zip dosyasına ekle (zip dosyası hariç)
    // Klasördeki tüm dosyaları tek tek ekleyelim (zip dosyası hariç)
    const files = fs.readdirSync(outputDir);
    for (const file of files) {
      const filePath = path.join(outputDir, file);
      const stat = fs.statSync(filePath);

      // Zip dosyasını kendisini hariç tut
      if (filePath !== zipFilePath) {
        if (stat.isDirectory()) {
          archive.directory(filePath, file);
        } else {
          archive.file(filePath, { name: file });
        }
      }
    }

    // Zip işlemini tamamla
    await archive.finalize();

    return { success: true, outputDir, zipFilePath };
  } catch (error) {
    console.error('Zip hatası:', error);
    return { success: false, error: error.message };
  }
});
