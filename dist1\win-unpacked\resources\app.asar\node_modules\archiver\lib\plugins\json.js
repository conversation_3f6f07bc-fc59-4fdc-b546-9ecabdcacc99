/**
 * JSON Format Plugin
 *
 * @module plugins/json
 * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}
 * @copyright (c) 2012-2014 <PERSON>, contributors.
 */
var inherits = require('util').inherits;
var Transform = require('readable-stream').Transform;

var crc32 = require('buffer-crc32');
var util = require('archiver-utils');

/**
 * @constructor
 * @param {(JsonOptions|TransformOptions)} options
 */
var Json = function(options) {
  if (!(this instanceof Json)) {
    return new Json(options);
  }

  options = this.options = util.defaults(options, {});

  Transform.call(this, options);

  this.supports = {
    directory: true,
    symlink: true
  };

  this.files = [];
};

inherits(Json, Transform);

/**
 * [_transform description]
 *
 * @private
 * @param  {Buffer}   chunk
 * @param  {String}   encoding
 * @param  {Function} callback
 * @return void
 */
Json.prototype._transform = function(chunk, encoding, callback) {
  callback(null, chunk);
};

/**
 * [_writeStringified description]
 *
 * @private
 * @return void
 */
Json.prototype._writeStringified = function() {
  var fileString = JSON.stringify(this.files);
  this.write(fileString);
};

/**
 * [append description]
 *
 * @param  {(Buffer|Stream)}   source
 * @param  {EntryData}   data
 * @param  {Function} callback
 * @return void
 */
Json.prototype.append = function(source, data, callback) {
  var self = this;

  data.crc32 = 0;

  function onend(err, sourceBuffer) {
    if (err) {
      callback(err);
      return;
    }

    data.size = sourceBuffer.length || 0;
    data.crc32 = crc32.unsigned(sourceBuffer);

    self.files.push(data);

    callback(null, data);
  }

  if (data.sourceType === 'buffer') {
    onend(null, source);
  } else if (data.sourceType === 'stream') {
    util.collectStream(source, onend);
  }
};

/**
 * [finalize description]
 *
 * @return void
 */
Json.prototype.finalize = function() {
  this._writeStringified();
  this.end();
};

module.exports = Json;

/**
 * @typedef {Object} JsonOptions
 * @global
 */
